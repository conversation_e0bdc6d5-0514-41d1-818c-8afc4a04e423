import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import ContactForm from "@/components/contact-form"

export default function QuoteRequestPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Page Content */}
      <div className="flex-1 py-8 sm:py-16 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Back Navigation */}
          <div className="mb-6">
            <Link href="/services" className="inline-flex items-center text-[#0a2a5e] hover:text-[#f5a623] transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Services
            </Link>
          </div>

          <h1 className="text-3xl sm:text-4xl font-bold text-center text-[#0a2a5e] mb-8 sm:mb-16">
            Research Lab Services Quote Request
          </h1>

          <div className="bg-white rounded-lg shadow-md border border-gray-100 p-6 sm:p-8">
            <ContactForm />
          </div>

          {/* Contact Information */}
          <div className="mt-12 bg-gray-50 rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-[#0a2a5e] mb-4">
              Need Help with Your Request?
            </h3>
            <p className="text-gray-700 mb-4">
              Our team is here to assist you with any questions about our services.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact">
                <Button variant="outline" className="border-[#0a2a5e] text-[#0a2a5e] hover:bg-[#0a2a5e] hover:text-white">
                  Contact Us
                </Button>
              </Link>
              <a href="mailto:<EMAIL>" className="inline-block">
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white">
                  Email Us Directly
                </Button>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
